# Java AES加密算法说明文档

## 概述

本文档详细分析了`game.txt`文件中的Java AES加密算法实现，该算法用于接口对接中的数据加密。

## 接口信息

- **接口文档地址**: https://b.163.com/knowledge/public/WXjbs9n3GC/knowdetail?docId=hFBUlGYSIa&pid=244179
- **加密算法**: AES-128
- **密钥生成**: SHA1PRNG
- **加密模式**: ECB
- **输出格式**: 十六进制字符串

## Java代码分析

### 1. 主要加密方法 `encrypt`

```java
public static String encrypt(String content, String encryptKey) {
    try {
        KeyGenerator kgen = KeyGenerator.getInstance("AES");
        SecureRandom random = SecureRandom.getInstance("SHA1PRNG");
        random.setSeed(encryptKey.getBytes());
        kgen.init(128, random);
        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(kgen.generateKey().getEncoded(), "AES"));
        return byteArr2HexStr(cipher.doFinal(content.getBytes("utf-8")));
    } catch (Exception e) {
        throw new RuntimeException(e);
    }
}
```

#### 参数说明
- `content`: 待加密的内容（字符串）
- `encryptKey`: 加密密钥（字符串）

#### 返回值
- 加密后的十六进制字符串

#### 算法流程
1. **创建AES密钥生成器**: `KeyGenerator.getInstance("AES")`
2. **创建SHA1PRNG随机数生成器**: `SecureRandom.getInstance("SHA1PRNG")`
3. **设置种子**: `random.setSeed(encryptKey.getBytes())`
4. **初始化密钥生成器**: `kgen.init(128, random)` - 使用128位密钥长度
5. **生成密钥**: `kgen.generateKey().getEncoded()`
6. **创建AES加密器**: `Cipher.getInstance("AES")`
7. **初始化加密模式**: `cipher.init(Cipher.ENCRYPT_MODE, ...)`
8. **执行加密**: `cipher.doFinal(content.getBytes("utf-8"))`
9. **转换为十六进制**: `byteArr2HexStr(...)`

### 2. 十六进制转换方法 `byteArr2HexStr`

```java
private static String byteArr2HexStr(byte[] b) {
    int length = b.length;
    StringBuffer sb = new StringBuffer(length * 2);
    for (int i = 0; i < length; i++) {
        int temp = b[i];
        while (temp < 0) {
            temp = temp + 256;
        }
        if (temp < 16) {
            sb.append("0");
        }
        sb.append(Integer.toString(temp, 16));
    }
    return sb.toString();
}
```

#### 功能说明
- 将字节数组转换为十六进制字符串
- 处理负数字节（Java中byte是有符号的）
- 确保每个字节都转换为两位十六进制数

#### 转换逻辑
1. 遍历字节数组中的每个字节
2. 将字节转换为整数（处理负数情况）
3. 如果数值小于16，在前面补0
4. 将整数转换为十六进制字符串
5. 拼接所有结果

## 关键技术点

### 1. SHA1PRNG算法
- **作用**: 用于生成AES密钥
- **特点**: 基于SHA1的伪随机数生成器
- **种子**: 使用`encryptKey.getBytes()`作为种子
- **输出**: 生成128位（16字节）的AES密钥

### 2. AES加密参数
- **算法**: AES
- **密钥长度**: 128位
- **加密模式**: ECB（默认）
- **填充方式**: PKCS5Padding（默认）
- **字符编码**: UTF-8

### 3. 字节处理
- **输入编码**: UTF-8
- **负数处理**: Java的byte是有符号的，需要转换为无符号
- **十六进制输出**: 小写字母

## 算法特点

### 优点
1. **安全性**: 使用AES-128加密，安全性较高
2. **确定性**: 相同的输入和密钥总是产生相同的输出
3. **兼容性**: 使用标准的AES算法

### 注意事项
1. **密钥生成**: SHA1PRNG的实现可能因Java版本而异
2. **字节序**: 需要注意字节序的处理
3. **编码**: 必须使用UTF-8编码

## PHP转换挑战

### 主要难点
1. **SHA1PRNG实现**: Java的SHA1PRNG有特定的内部实现
2. **字节处理**: PHP和Java的字节处理方式不同
3. **密钥生成**: 需要精确模拟Java的密钥生成过程

### 转换要点
1. **密钥生成**: 使用SHA1模拟SHA1PRNG
2. **AES加密**: 使用OpenSSL的AES-128-ECB
3. **字节转换**: 实现与Java相同的十六进制转换逻辑
4. **编码处理**: 确保UTF-8编码一致性

## 测试数据

### 输入参数
- **content**: `[{"orderId":3729851333396276480,"uid":40993059866,"orderTime":1753863885000,"payTime":1753863885000,"amount":1,"price":99.00,"status":2,"refundNo":"","refundTime":0}]`
- **encryptKey**: `2061A21ED53C4A4594C27946A21E2A88`

### 期望输出
- **Java结果**: `6b1a43ac5296e787ca52d9ebc92f8d0d8d35ca24bec204d1ede4aaa5be3018f1bfc83f5c9ba635ff7390ea96ffff7e5591a0f19dbc8e8692c0f57934f489b31cc68752da23255cb499a3caacae2885a12a32362d07e92c5d415c18bd444ac1834a3ea420f6d054923ff41ee7485a6820fea92865885564005731039a3bb64a1ef30b400fd2067b11fc1479ee4b184dc9a3639639e5acb8a0e6a8501e594bb71f5a15c76679eee6ba7dc7817cee4b2851`

## 总结

这个Java AES加密算法是一个标准的AES-128加密实现，主要特点是使用SHA1PRNG生成密钥。在转换为PHP时，最大的挑战是准确模拟Java的SHA1PRNG算法实现，确保密钥生成的一致性。 