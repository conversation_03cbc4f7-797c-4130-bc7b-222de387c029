import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.spec.SecretKeySpec;
import java.security.SecureRandom;
import java.security.MessageDigest;
import java.util.Map;
import java.util.TreeMap;

public class Sign {

    /**
     * 使用AES算法对content加密
     *
     * @param content    待加密的内容
     * @param encryptKey 加密密钥
     * @return 加密后的byte[]
     */
    public static String encrypt(String content, String encryptKey) {
        try {
            KeyGenerator kgen = KeyGenerator.getInstance("AES");
            SecureRandom random = SecureRandom.getInstance("SHA1PRNG");
            random.setSeed(encryptKey.getBytes());
            kgen.init(128, random);
            byte[] keyBytes = kgen.generateKey().getEncoded();
            System.out.println("DEBUG - Generated Key (Hex): " + byteArr2HexStr(keyBytes));
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(keyBytes, "AES"));
            return byteArr2HexStr(cipher.doFinal(content.getBytes("utf-8")));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
   /** 
     * 将byte数组转换为16进制值的字符串
     *
     * @param b 需要转换的byte数组
     * @return 转换后的字符串
     */
    private static String byteArr2HexStr(byte[] b) {
        int length = b.length;
        StringBuffer sb = new StringBuffer(length * 2);
        for (int i = 0; i < length; i++) {
            int temp = b[i];
            while (temp < 0) {
                temp = temp + 256;
            }
            if (temp < 16) {
                sb.append("0");
            }
            sb.append(Integer.toString(temp, 16));
        }
        return sb.toString();
    }

    public static void main(String[] args) {
        String contentToEncrypt = "[{\"orderId\":3729851333396276480,\"uid\":40993059866,\"orderTime\":1753863885000,\"payTime\":1753863885000,\"amount\":1,\"price\":99.00,\"status\":2,\"refundNo\":\"\",\"refundTime\":0}]";
        String secretKey = "2061A21ED53C4A4594C27946A21E2A88";
        
        try {
            String encryptedHex = encrypt(contentToEncrypt, secretKey);
            
            System.out.println("Original Content: " + contentToEncrypt);
            System.out.println("Secret Key: " + secretKey);
            System.out.println("Encrypted (Hex): " + encryptedHex);
            
        } catch (RuntimeException e) {
            System.err.println("Error: " + e.getMessage());
        }
    }
}
