import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.spec.SecretKeySpec;
import java.security.SecureRandom;
import java.security.MessageDigest;
import java.util.Map;
import java.util.TreeMap;
import java.util.HashMap;

/**
 * 接口鉴权签名工具类
 * 基于网易接口文档: https://b.163.com/knowledge/public/WXjbs9n3GC/knowdetail?docId=hFBUlGYSIa&pid=244179
 */
public class SignComplete {

    /**
     * 使用AES算法对content加密
     *
     * @param content    待加密的内容
     * @param encryptKey 加密密钥
     * @return 加密后的十六进制字符串
     */
    public static String encrypt(String content, String encryptKey) {
        try {
            KeyGenerator kgen = KeyGenerator.getInstance("AES");
            SecureRandom random = SecureRandom.getInstance("SHA1PRNG");
            random.setSeed(encryptKey.getBytes());
            kgen.init(128, random);
            byte[] keyBytes = kgen.generateKey().getEncoded();
            System.out.println("DEBUG - Generated Key (Hex): " + byteArr2HexStr(keyBytes));
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(keyBytes, "AES"));
            return byteArr2HexStr(cipher.doFinal(content.getBytes("utf-8")));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /** 
     * 将byte数组转换为16进制值的字符串
     *
     * @param b 需要转换的byte数组
     * @return 转换后的字符串
     */
    private static String byteArr2HexStr(byte[] b) {
        int length = b.length;
        StringBuffer sb = new StringBuffer(length * 2);
        for (int i = 0; i < length; i++) {
            int temp = b[i];
            while (temp < 0) {
                temp = temp + 256;
            }
            if (temp < 16) {
                sb.append("0");
            }
            sb.append(Integer.toString(temp, 16));
        }
        return sb.toString();
    }

    /**
     * 生成接口签名
     * 用于接口鉴权验证
     * 
     * @param params 请求参数Map
     * @param secretKey 密钥
     * @return 签名字符串
     */
    public static String generateSign(Map<String, String> params, String secretKey) {
        try {
            // 1. 参数排序
            TreeMap<String, String> sortedParams = new TreeMap<>(params);
            
            // 2. 构建签名字符串
            StringBuilder signStr = new StringBuilder();
            for (Map.Entry<String, String> entry : sortedParams.entrySet()) {
                if (entry.getValue() != null && !entry.getValue().isEmpty()) {
                    signStr.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
                }
            }
            
            // 3. 添加密钥
            signStr.append("key=").append(secretKey);
            
            System.out.println("DEBUG - Sign String: " + signStr.toString());
            
            // 4. MD5签名
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(signStr.toString().getBytes("UTF-8"));
            
            // 5. 转换为十六进制
            return byteArr2HexStr(digest).toUpperCase();
            
        } catch (Exception e) {
            throw new RuntimeException("签名生成失败", e);
        }
    }

    /**
     * 验证接口签名
     * 
     * @param params 请求参数
     * @param receivedSign 接收到的签名
     * @param secretKey 密钥
     * @return 验证结果
     */
    public static boolean verifySign(Map<String, String> params, String receivedSign, String secretKey) {
        String calculatedSign = generateSign(params, secretKey);
        return calculatedSign.equals(receivedSign);
    }

    /**
     * 完整的接口鉴权验证
     * 包含AES加密和签名验证
     * 
     * @param content 待加密内容
     * @param params 请求参数
     * @param secretKey 密钥
     * @return 鉴权结果对象
     */
    public static AuthResult performAuth(String content, Map<String, String> params, String secretKey) {
        try {
            // 1. AES加密
            String encryptedContent = encrypt(content, secretKey);
            
            // 2. 生成签名
            String signature = generateSign(params, secretKey);
            
            // 3. 返回结果
            return new AuthResult(true, encryptedContent, signature, "鉴权成功");
            
        } catch (Exception e) {
            return new AuthResult(false, null, null, "鉴权失败: " + e.getMessage());
        }
    }

    /**
     * 鉴权结果类
     */
    public static class AuthResult {
        private boolean success;
        private String encryptedContent;
        private String signature;
        private String message;
        
        public AuthResult(boolean success, String encryptedContent, String signature, String message) {
            this.success = success;
            this.encryptedContent = encryptedContent;
            this.signature = signature;
            this.message = message;
        }
        
        // Getters
        public boolean isSuccess() { return success; }
        public String getEncryptedContent() { return encryptedContent; }
        public String getSignature() { return signature; }
        public String getMessage() { return message; }
        
        @Override
        public String toString() {
            return String.format("AuthResult{success=%s, encryptedContent='%s', signature='%s', message='%s'}", 
                success, encryptedContent, signature, message);
        }
    }

    public static void main(String[] args) {
        String contentToEncrypt = "[{\"orderId\":3729851333396276480,\"uid\":40993059866,\"orderTime\":1753863885000,\"payTime\":1753863885000,\"amount\":1,\"price\":99.00,\"status\":2,\"refundNo\":\"\",\"refundTime\":0}]";
        String secretKey = "2061A21ED53C4A4594C27946A21E2A88";
        
        System.out.println("=== AES加密测试 ===");
        try {
            String encryptedHex = encrypt(contentToEncrypt, secretKey);
            
            System.out.println("Original Content: " + contentToEncrypt);
            System.out.println("Secret Key: " + secretKey);
            System.out.println("Encrypted (Hex): " + encryptedHex);
            
            // 期望结果对比
            String expectedResult = "6b1a43ac5296e787ca52d9ebc92f8d0d8d35ca24bec204d1ede4aaa5be3018f1bfc83f5c9ba635ff7390ea96ffff7e5591a0f19dbc8e8692c0f57934f489b31cc68752da23255cb499a3caacae2885a12a32362d07e92c5d415c18bd444ac1834a3ea420f6d054923ff41ee7485a6820fea92865885564005731039a3bb64a1ef30b400fd2067b11fc1479ee4b184dc9a3639639e5acb8a0e6a8501e594bb71f5a15c76679eee6ba7dc7817cee4b2851";
            System.out.println("Expected Result: " + expectedResult);
            System.out.println("Match: " + encryptedHex.equals(expectedResult));
            
        } catch (RuntimeException e) {
            System.err.println("AES加密错误: " + e.getMessage());
        }
        
        System.out.println("\n=== 接口鉴权测试 ===");
        // 模拟接口参数
        Map<String, String> params = new HashMap<>();
        params.put("appId", "test123");
        params.put("timestamp", "1640995200000");
        params.put("nonce", "abc123");
        params.put("data", contentToEncrypt);
        
        try {
            // 完整鉴权测试
            AuthResult result = performAuth(contentToEncrypt, params, secretKey);
            System.out.println("鉴权结果: " + result);
            
            // 签名验证测试
            boolean isValid = verifySign(params, result.getSignature(), secretKey);
            System.out.println("签名验证: " + (isValid ? "通过" : "失败"));
            
        } catch (Exception e) {
            System.err.println("接口鉴权错误: " + e.getMessage());
        }
    }
}
